{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(grep:*)", "Bash(awk:*)", "<PERSON><PERSON>(python3:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(node test_disk_space.js)", "Bash(node:*)", "Bash(rm:*)", "Bash(rg:*)", "Bash(git checkout:*)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.8/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"checkAndRestartMissedRecordings\" /home/<USER>/downloader/douyin.js)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.8/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"sendAllAnchorsToProcess\" /home/<USER>/downloader/douyin.js)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.8/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"onStatusChange\\|onInsert\" /home/<USER>/downloader/douyin.js)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.8/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -i \"redis\" /home/<USER>/downloader/douyin.js)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.8/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -i \"redis|\\.publish|\\.subscribe|\\.channel\" /home/<USER>/downloader/douyin.js)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.8/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"scanRecordings\" /home/<USER>/downloader/douyin.js)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.8/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"getCurrentTimestampSecond|timestampToISO|formatTimeDifference\" /home/<USER>/downloader/douyin.js)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.8/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n \"startScanning\" /home/<USER>/downloader/douyin.js)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.8/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -i \"MON_SCAN|RECORDING|SCAN_|FILE_|scanRecordings|startScanning\" /home/<USER>/downloader/douyin.js)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(timeout:*)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.8/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -A 10 -B 5 \"supabase.*channel|postgres_changes|\\.on\\(.*INSERT|\\.on\\(.*UPDATE|\\.on\\(.*DELETE\" /home/<USER>/downloader/record.js)", "WebFetch(domain:supabase.com)", "<PERSON><PERSON>(chmod:*)", "Bash(/home/<USER>/.nvm/versions/node/v18.20.8/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg -n -B2 -A10 \"checkDiskSpace|getDiskSpace\" record.js record3.js)", "<PERSON><PERSON>(journalctl:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(screen:*)", "Bash(kill:*)", "Bash(iftop:*)", "mcp__ide__executeCode", "mcp__supabase__list_projects", "mcp__supabase__execute_sql", "mcp__supabase__apply_migration", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "Bash(ffmpeg:*)", "<PERSON><PERSON>(sox:*)", "mcp__supabase__list_tables", "<PERSON><PERSON>(python:*)", "Bash(for line in 774 1337 1469 1488 3092 3101 3174 3433)", "Bash(do echo \"=== Line $line ===\")", "Bash(done)", "Bash(# 查看第774行附近的代码\necho \"\"=== Line 774 ===\"\"\nsed -n ''770,778p'' record.js\n\necho -e \"\"\\n=== Line 1337 ===\"\"\nsed -n ''1333,1341p'' record.js\n\necho -e \"\"\\n=== Line 1469 ===\"\"\nsed -n ''1465,1473p'' record.js\n\necho -e \"\"\\n=== Line 1488 ===\"\"\nsed -n ''1484,1492p'' record.js\n\necho -e \"\"\\n=== Line 3092 ===\"\"\nsed -n ''3088,3096p'' record.js\n\necho -e \"\"\\n=== Line 3101 ===\"\"\nsed -n ''3097,3105p'' record.js\n\necho -e \"\"\\n=== Line 3174 ===\"\"\nsed -n ''3170,3178p'' record.js\n\necho -e \"\"\\n=== Line 3433 ===\"\"\nsed -n ''3429,3437p'' record.js)", "Bash(pip3 install:*)", "Bash(fc-list:*)", "mcp__supabase__get_project", "Bash(nload:*)", "<PERSON><PERSON>(pip show:*)", "Bash(ping:*)", "<PERSON><PERSON>(nvidia-smi:*)", "Bash(ip link:*)", "Bash(pm2 list:*)", "Bash(/usr/bin/ffprobe -v error -show_entries format=duration,bit_rate:stream=codec_name,bit_rate -of json \"./recordings/ad3cd4cf-65e2-4d68-b307-f96436e63c0d/1753447097-ad3cd4cf-65e2-4d68-b307-f96436e63c0d.ts\")", "Bash(/usr/bin/ffmpeg -i \"./recordings/ad3cd4cf-65e2-4d68-b307-f96436e63c0d/1753447097-ad3cd4cf-65e2-4d68-b307-f96436e63c0d.ts\" -c copy -t 60 -f null -)", "Bash(/usr/bin/ffmpeg -version)", "Bash(/bin/ffmpeg -version)", "<PERSON><PERSON>(sudo mv:*)", "Bash(sudo find / -name \"*.mp4\" -type f)", "Bash(sudo find /tmp -name \"*.mp4\" -type f -exec rm {} ;)", "Bash(sudo find /tmp -name \"*.mp4\" -type f)", "<PERSON><PERSON>(sed:*)", "Bash(apt list:*)", "Bash(pip install:*)", "Bash(psql:*)", "Bash(systemctl status:*)", "<PERSON><PERSON>(echo:*)", "Bash(dmesg:*)", "<PERSON><PERSON>(nproc)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(jq:*)", "Bash(# 使用ffprobe检查已下载视频的质量信息\nffprobe -v error -select_streams v:0 -show_entries stream=width,height,bit_rate,codec_name -of json /home/<USER>/downloader/downloads/阿胜l_LX5ow083px8/20250408_182953_wVWMe_BC2UlPrCYDWVbkEQ.mp4)", "Bash(pgrep:*)", "Bash(git reset:*)", "<PERSON><PERSON>(curl:*)", "Bash(nc:*)"], "deny": []}, "enableAllProjectMcpServers": false}