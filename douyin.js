const { createClient } = require('@supabase/supabase-js');
const axios = require('axios'); // 确保安装了axios: npm install axios
const { Telegraf } = require('telegraf'); // 添加Telegraf库引用
require('dotenv').config(); // 加载环境变量

const url = 'https://wjanjmsywbydjbfrdkaz.supabase.co';
const key = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6IndqYW5qbXN5d2J5ZGpiZnJka2F6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MjAxMjc1OTEsImV4cCI6MjAzNTcwMzU5MX0.ny-61EeDBsVRUr6BdWiamt9oV-6z2TC_f01FJff-9fE';
const supabase = createClient(url, key);


// 配置Telegram机器人
const TELEGRAM_BOT_TOKEN = process.env.breakiiiiiiiiingbot;
const TELEGRAM_CHAT_ID = process.env.pubpub;

// 调试：输出环境变量值
console.log(`[${new Date().toLocaleString()}] 环境变量调试:`);
console.log(`[${new Date().toLocaleString()}] TELEGRAM_BOT_TOKEN: ${TELEGRAM_BOT_TOKEN ? TELEGRAM_BOT_TOKEN.substring(0, 10) + '...' : 'undefined'}`);
console.log(`[${new Date().toLocaleString()}] TELEGRAM_CHAT_ID: ${TELEGRAM_CHAT_ID || 'undefined'}`);

if (!TELEGRAM_BOT_TOKEN || !TELEGRAM_CHAT_ID) {
  console.error(`[${new Date().toLocaleString()}] 错误: Telegram环境变量未设置`);
  console.error(`[${new Date().toLocaleString()}] 请设置 breakiiiiiiiiingbot 和 pubpub 环境变量`);
  process.exit(1);
}

const bot = new Telegraf(TELEGRAM_BOT_TOKEN);

// API端口轮换配置
const API_PORTS = [8080, 8081, 8082, 8083];
let currentPortIndex = 0;

// 获取当前使用的端口
const getCurrentPort = () => {
  return API_PORTS[currentPortIndex];
};

// 切换到下一个端口
const rotatePort = () => {
  const previousPort = API_PORTS[currentPortIndex];
  currentPortIndex = (currentPortIndex + 1) % API_PORTS.length;
  const newPort = API_PORTS[currentPortIndex];
  console.log(`[${new Date().toLocaleString()}] API端口轮换: ${previousPort} -> ${newPort}`);
  return newPort;
};

const getCurrentTime = () => {
  return new Date().toLocaleString();
}



/**
 * 从抖音链接中提取webcast_id
 * @param {string} url 抖音链接
 * @returns {Promise<string|null>} webcast_id或null
 */
const extractDouyinWebcastId = async (url) => {
  try {
    // 如果URL为空，直接返回null
    if (!url) {
      console.log(`[${getCurrentTime()}] 链接为空，无法提取webcast_id`);
      return null;
    }

    // 对于长链接，直接提取ID
    if (url.includes('live.douyin.com')) {
      const parts = url.split('live.douyin.com/');
      if (parts.length < 2) {
        console.log(`[${getCurrentTime()}] 无效的抖音长链接格式: ${url}`);
        return null;
      }
      const id = parts[1].split(/[/?#]/)[0];
      if (!id || id.length === 0) {
        console.log(`[${getCurrentTime()}] 无法从长链接提取ID: ${url}`);
        return null;
      }
      return id;
    } 
    // 对于短链接，需要跟踪重定向
    else if (url.includes('v.douyin.com')) {
      const response = await axios.get(url, {
        maxRedirects: 5,
        validateStatus: (status) => status >= 200 && status < 400
      });
      
      // 从最终URL中提取ID
      const redirectUrl = response.request.res.responseUrl;
      if (redirectUrl && redirectUrl.includes('live.douyin.com')) {
        const parts = redirectUrl.split('live.douyin.com/');
        if (parts.length < 2) {
          console.log(`[${getCurrentTime()}] 重定向后的链接格式无效: ${redirectUrl}`);
          return null;
        }
        const id = parts[1].split(/[/?#]/)[0];
        if (!id || id.length === 0) {
          console.log(`[${getCurrentTime()}] 无法从重定向链接提取ID: ${redirectUrl}`);
          return null;
        }
        return id;
      } else {
        console.log(`[${getCurrentTime()}] 重定向后的链接不是抖音直播链接: ${redirectUrl || '无重定向URL'}`);
        return null;
      }
    } else {
      console.log(`[${getCurrentTime()}] 不支持的抖音链接格式: ${url}`);
      return null;
    }
  } catch (error) {
    console.error(`[${getCurrentTime()}] 提取抖音webcast_id失败: ${error.message}`);
    return null;
  }
};

/**
 * 处理抖音链接并获取直播信息
 * @param {string} anchorId 主播ID
 * @param {string} liveUrl 直播链接
 * @returns {Promise<object|null>} 处理结果或null
 */
const processDouyinLink = async (anchorId, liveUrl) => {
  try {
    console.log(`[${getCurrentTime()}] 开始处理抖音链接: ${liveUrl}`);
    
    // 提取webcast_id
    const webcastId = await extractDouyinWebcastId(liveUrl);
    if (!webcastId) {
      console.log(`[${getCurrentTime()}] 无法从链接提取webcast_id: ${liveUrl}，跳过API处理`);
      return null;
    }
    
    // 调用本地API获取直播信息
    let response;
    try {
      const apiPort = getCurrentPort();
      console.log(`[${getCurrentTime()}] 提取到webcast_id: ${webcastId}，调用API (端口: ${apiPort})`);
      response = await axios.get(
        `http://localhost:${apiPort}/api/douyin/web/fetch_user_live_videos?webcast_id=${webcastId}`,
        { 
          headers: { 'accept': 'application/json' },
          timeout: 10000 // 设置10秒超时
        }
      );
    } catch (apiError) {
      console.error(`[${getCurrentTime()}] API请求失败: ${apiError.message}`);
      if (apiError.response) {
        console.error(`[${getCurrentTime()}] API响应状态: ${apiError.response.status}`);
        console.error(`[${getCurrentTime()}] API响应数据: ${JSON.stringify(apiError.response.data || {})}`);
      }
      throw apiError;
    }
    
    const jsonData = response.data;
    if (jsonData.code !== 200) {
      console.error(`[${getCurrentTime()}] API返回非200状态码: ${jsonData.code}, 消息: ${jsonData.message || '无'}`);
      return null;
    }
    
    // 获取主播基本信息和房间状态
    const userData = jsonData.data?.data?.user;
    const roomStatus = jsonData.data?.data?.room_status;
    
    // 检查data数组是否为空
    const hasLiveData = Array.isArray(jsonData.data?.data?.data) && jsonData.data.data.data.length > 0;
    
    // 新增：输出详细诊断日志
    console.log(`[${getCurrentTime()}] API返回数据状态: room_status=${roomStatus}, hasLiveData=${hasLiveData}`);
    
    // 新增：特殊情况处理 - data数组有内容且其中直播项status=2，但room_status可能不是2
    if (hasLiveData) {
      const roomData = jsonData.data.data.data[0];
      const individualStatus = roomData.status;
      
      console.log(`[${getCurrentTime()}] 单个直播项状态: data[0].status=${individualStatus}`);
      
      // 如果单个直播项状态为2(视频直播)，优先使用此状态判断
      if (individualStatus === 2) {
        console.log(`[${getCurrentTime()}] 检测到特殊情况: 单个直播项status=2(直播中)但room_status=${roomStatus}`);
        
        // 获取主播名称
        let anchorName = '';
        if (userData && userData.nickname) {
          anchorName = userData.nickname;
        } else if (roomData.owner && roomData.owner.nickname) {
          anchorName = roomData.owner.nickname;
        }
        
        if (!anchorName) {
          console.error(`[${getCurrentTime()}] 无法获取主播名称，跳过处理`);
          return null;
        }
        
        // 构建结果对象
        const result = {
          anchor_id: anchorId,
          anchor_name: anchorName,
          status: true, // 设置为直播状态
          title: roomData.title || '',
          new_url: `https://live.douyin.com/${webcastId}`
        };
        
        // 提取stream_url
        if (roomData.stream_url) {
          result.stream_url = roomData.stream_url;
          console.log(`[${getCurrentTime()}] 成功提取stream_url数据 (特殊情况处理)`);
          
        } else {
          console.warn(`[${getCurrentTime()}] 特殊情况: 单个直播项status=2但无stream_url数据`);
        }
        
        console.log(`[${getCurrentTime()}] 抖音链接处理结果: ${JSON.stringify({
          anchor_id: result.anchor_id,
          anchor_name: result.anchor_name,
          status: '特殊情况：直播中',
          new_url: result.new_url
        })}`);
        
        return result;
      }
    }
    
    // 以下是原有逻辑，保持不变
    if (!hasLiveData) {
      console.log(`[${getCurrentTime()}] API返回的data数组为空，检查是否为音频直播`);
      
      // 检查是否为音频直播 (room_status = 1)
      const isAudioLive = roomStatus === 1;
      
      if (isAudioLive) {
        console.log(`[${getCurrentTime()}] 检测到音频直播 (room_status=1)，获取主播信息`);
        
        // 获取主播信息
        if (userData && userData.nickname) {
          const result = {
            anchor_id: anchorId,
            anchor_name: userData.nickname,
            status: true, // 音频直播也是直播状态
            title: '',
            new_url: `https://live.douyin.com/${webcastId}`
          };
          
          // 准备访问douyin_user表的URL
          const douyinUrl = result.new_url;
          console.log(`[${getCurrentTime()}] 音频直播，准备使用new_url(${douyinUrl})从douyin_user表获取room_data`);
          
          // 尝试从douyin_user表获取room_data
          try {
            const { data: audioData, error: audioError } = await supabase
              .from('douyin_user')
              .select('room_data')
              .eq('new_url', douyinUrl)
              .single();
            
            if (audioError) {
              console.error(`[${getCurrentTime()}] 使用new_url(${douyinUrl})获取douyin_user数据失败: ${audioError.message}`);
              
              // 再尝试用不同的方式查询
              console.log(`[${getCurrentTime()}] 尝试使用new_url的简短版本查询douyin_user表`);
              const shortUrl = `https://live.douyin.com/${webcastId}`;
              
              const { data: audioData2, error: audioError2 } = await supabase
                .from('douyin_user')
                .select('room_data')
                .eq('new_url', shortUrl)
                .single();
                
              if (audioError2) {
                console.error(`[${getCurrentTime()}] 使用简短new_url(${shortUrl})获取douyin_user数据也失败: ${audioError2.message}`);
                
                // 尝试模糊匹配
                console.log(`[${getCurrentTime()}] 尝试使用模糊匹配查询douyin_user表`);
                const { data: fuzzyData, error: fuzzyError } = await supabase
                  .from('douyin_user')
                  .select('new_url, room_data')
                  .ilike('new_url', `%${webcastId}%`)
                  .limit(1);
                  
                if (fuzzyError || !fuzzyData || fuzzyData.length === 0) {
                  console.error(`[${getCurrentTime()}] 模糊匹配douyin_user表失败或无结果: ${fuzzyError?.message || '无匹配记录'}`);
                } else {
                  console.log(`[${getCurrentTime()}] 模糊匹配成功! 找到记录: ${fuzzyData[0].new_url}`);
                  processRoomData(fuzzyData[0].room_data, result);
                }
              } else if (audioData2 && audioData2.room_data) {
                console.log(`[${getCurrentTime()}] 使用简短new_url成功获取douyin_user数据!`);
                processRoomData(audioData2.room_data, result);
              }
            } else if (audioData && audioData.room_data) {
              console.log(`[${getCurrentTime()}] 使用new_url成功获取douyin_user数据!`);
              processRoomData(audioData.room_data, result);
            } else {
              console.log(`[${getCurrentTime()}] douyin_user表中未找到room_data或数据为空 (URL: ${douyinUrl})`);
            }
            
            // 内部函数处理room_data
            function processRoomData(roomData, resultObj) {
              // 将room_data转为文本并存储到stream_url
              let roomDataText = '';
              
              console.log(`[${getCurrentTime()}] 从douyin_user表获取到room_data，类型: ${typeof roomData}`);
              console.log(`[${getCurrentTime()}] room_data内容: ${JSON.stringify(roomData).substring(0, 200)}...`);
              
              // 确保从jsonb转换为text格式
              if (typeof roomData === 'object') {
                roomDataText = JSON.stringify(roomData);
                console.log(`[${getCurrentTime()}] room_data是对象，已转换为JSON字符串`);
              } else if (typeof roomData === 'string') {
                // 如果已经是字符串，检查是否是有效的JSON
                try {
                  // 尝试解析后再转换，确保格式正确
                  const parsedData = JSON.parse(roomData);
                  roomDataText = JSON.stringify(parsedData);
                  console.log(`[${getCurrentTime()}] room_data是JSON字符串，已规范化格式`);
                } catch (e) {
                  // 如果解析失败，说明不是JSON格式，直接使用
                  roomDataText = roomData;
                  console.log(`[${getCurrentTime()}] room_data是普通字符串，直接使用`);
                }
              } else {
                // 其他情况，尝试强制转换为字符串
                roomDataText = String(roomData);
                console.log(`[${getCurrentTime()}] room_data是其他类型，强制转换为字符串`);
              }
              
              console.log(`[${getCurrentTime()}] 成功获取音频直播数据，转换后类型: ${typeof roomDataText}`);
              console.log(`[${getCurrentTime()}] 转换后room_data示例: ${roomDataText.substring(0, 100)}...`);
              
              resultObj.stream_url = roomDataText;
            }
          } catch (audioError) {
            console.error(`[${getCurrentTime()}] 处理音频直播数据时出错: ${audioError.message}`);
          }
          
          console.log(`[${getCurrentTime()}] 抖音链接处理结果: ${JSON.stringify({
            anchor_id: result.anchor_id,
            anchor_name: result.anchor_name,
            status: '音频直播',
            new_url: result.new_url
          })}`);
          
          
          return result;
        }
      }
      
      // 即使data数组为空，也尝试获取主播信息
      if (userData && userData.nickname) {
        const result = {
          anchor_id: anchorId,
          anchor_name: userData.nickname,
          status: false, // 设置为未直播状态
          title: '',
          new_url: `https://live.douyin.com/${webcastId}`
        };
        
        console.log(`[${getCurrentTime()}] 抖音链接处理结果: ${JSON.stringify({
          anchor_id: result.anchor_id,
          anchor_name: result.anchor_name,
          status: '未直播（data数组为空）',
          new_url: result.new_url
        })}`);
        
        return result;
      }
      
      console.error(`[${getCurrentTime()}] API返回数据结构不完整且无法获取主播信息`);
      return null;
    }
    
    // 原始逻辑继续处理
    const roomData = jsonData.data.data.data[0];
    
    // 获取主播名称 - 优先从userData获取，这在直播和非直播状态下都存在
    let anchorName = '';
    if (userData && userData.nickname) {
      anchorName = userData.nickname;
    } else if (roomData.owner && roomData.owner.nickname) {
      anchorName = roomData.owner.nickname;
    }
    
    if (!anchorName) {
      console.error(`[${getCurrentTime()}] 无法获取主播名称，跳过处理`);
      return null;
    }
    
    // 判断直播状态 (status=2表示正在直播，status=4表示未在直播, status=1表示音频直播)
    // 修正：必须同时满足roomData.status=2和data数组不为空
    const isLiveStream = (roomData.status === 2) && hasLiveData;
    
    // 判断是否为音频直播
    const isAudioLiveStream = (roomData.status === 1);
    
    if (roomData.status === 2 && !hasLiveData) {
      console.log(`[${getCurrentTime()}] 警告：room_status=2但data数组为空，视为不在直播状态`);
    }
    
    // 构建结果对象
    const result = {
      anchor_id: anchorId,
      anchor_name: anchorName,
      status: isLiveStream || isAudioLiveStream, // 音频直播也算直播状态
      title: roomData.title || '',
      new_url: `https://live.douyin.com/${webcastId}`
    };
    
    // 处理音频直播 (room_status = 1) 的情况
    if (isAudioLiveStream) {
      console.log(`[${getCurrentTime()}] 检测到音频直播 (room_status=1)，获取douyin_user表中的room_data`);
      
      // 准备访问douyin_user表的URL
      const douyinUrl = result.new_url;
      console.log(`[${getCurrentTime()}] 音频直播，准备使用new_url(${douyinUrl})从douyin_user表获取room_data`);
      
      try {
        // 从douyin_user表查询room_data，使用new_url作为关联字段
        const { data: userData, error: userError } = await supabase
          .from('douyin_user')
          .select('room_data')
          .eq('new_url', douyinUrl)
          .single();
        
        if (userError) {
          console.error(`[${getCurrentTime()}] 使用new_url(${douyinUrl})获取douyin_user数据失败: ${userError.message}`);
          
          // 尝试使用简短URL版本
          console.log(`[${getCurrentTime()}] 尝试使用new_url的简短版本查询douyin_user表`);
          const shortUrl = `https://live.douyin.com/${webcastId}`;
          
          const { data: userData2, error: userError2 } = await supabase
            .from('douyin_user')
            .select('room_data')
            .eq('new_url', shortUrl)
            .single();
            
          if (userError2) {
            console.error(`[${getCurrentTime()}] 使用简短new_url(${shortUrl})获取douyin_user数据也失败: ${userError2.message}`);
            
            // 尝试模糊匹配
            console.log(`[${getCurrentTime()}] 尝试使用模糊匹配查询douyin_user表`);
            const { data: fuzzyData, error: fuzzyError } = await supabase
              .from('douyin_user')
              .select('new_url, room_data')
              .ilike('new_url', `%${webcastId}%`)
              .limit(1);
              
            if (fuzzyError || !fuzzyData || fuzzyData.length === 0) {
              console.error(`[${getCurrentTime()}] 模糊匹配douyin_user表失败或无结果: ${fuzzyError?.message || '无匹配记录'}`);
            } else {
              console.log(`[${getCurrentTime()}] 模糊匹配成功! 找到记录: ${fuzzyData[0].new_url}`);
              processRoomData(fuzzyData[0].room_data);
            }
          } else if (userData2 && userData2.room_data) {
            console.log(`[${getCurrentTime()}] 使用简短new_url成功获取douyin_user数据!`);
            processRoomData(userData2.room_data);
          }
        } else if (userData && userData.room_data) {
          console.log(`[${getCurrentTime()}] 使用new_url成功获取douyin_user数据!`);
          processRoomData(userData.room_data);
        } else {
          console.log(`[${getCurrentTime()}] douyin_user表中未找到room_data或数据为空 (URL: ${douyinUrl})`);
        }
        
        // 内部函数处理room_data
        function processRoomData(roomData) {
          // 将room_data转为文本并存储到stream_url
          let roomDataText = '';
          
          console.log(`[${getCurrentTime()}] 从douyin_user表获取到room_data，类型: ${typeof roomData}`);
          console.log(`[${getCurrentTime()}] room_data内容: ${JSON.stringify(roomData).substring(0, 200)}...`);
          
          // 确保从jsonb转换为text格式
          if (typeof roomData === 'object') {
            roomDataText = JSON.stringify(roomData);
            console.log(`[${getCurrentTime()}] room_data是对象，已转换为JSON字符串`);
          } else if (typeof roomData === 'string') {
            // 如果已经是字符串，检查是否是有效的JSON
            try {
              // 尝试解析后再转换，确保格式正确
              const parsedData = JSON.parse(roomData);
              roomDataText = JSON.stringify(parsedData);
              console.log(`[${getCurrentTime()}] room_data是JSON字符串，已规范化格式`);
            } catch (e) {
              // 如果解析失败，说明不是JSON格式，直接使用
              roomDataText = roomData;
              console.log(`[${getCurrentTime()}] room_data是普通字符串，直接使用`);
            }
          } else {
            // 其他情况，尝试强制转换为字符串
            roomDataText = String(roomData);
            console.log(`[${getCurrentTime()}] room_data是其他类型，强制转换为字符串`);
          }
          
          console.log(`[${getCurrentTime()}] 成功获取音频直播数据，转换后类型: ${typeof roomDataText}`);
          console.log(`[${getCurrentTime()}] 转换后room_data示例: ${roomDataText.substring(0, 100)}...`);
          
          result.stream_url = roomDataText;
        }
      } catch (audioError) {
        console.error(`[${getCurrentTime()}] 处理音频直播数据时出错: ${audioError.message}`);
      }
    }
    
    // 只有直播状态才添加stream_url字段
    if (isLiveStream && roomData.stream_url) {
      result.stream_url = roomData.stream_url;
    }
    
    // 记录详细日志
    console.log(`[${getCurrentTime()}] 抖音链接处理结果: ${JSON.stringify({
      anchor_id: result.anchor_id,
      anchor_name: result.anchor_name,
      status: result.status ? '直播中' : '未直播',
      title: result.title,
      stream_url_available: isLiveStream && !!roomData.stream_url
    })}`);
    
    
    return result;
  } catch (error) {
    console.error(`[${getCurrentTime()}] 处理抖音链接失败: ${liveUrl}, 错误: ${error.message}`);
    // 额外记录更多错误信息
    if (error.stack) {
      console.error(`[${getCurrentTime()}] 错误堆栈: ${error.stack}`);
    }
    return null;
  }
};

/**
 * 更新抖音主播信息到数据库
 * @param {object} result 处理结果
 * @returns {Promise<boolean>} 是否更新成功
 */
const updateDouyinAnchorInfo = async (result) => {
  try {
    if (!result || !result.anchor_id) return false;
    
    const updateData = {};
    if (result.anchor_name) updateData.anchor_name = result.anchor_name;
    if (result.stream_url) updateData.stream_url = result.stream_url;
    if (result.status !== undefined) updateData.status = result.status;
    if (result.title) updateData.title = result.title;
    if (result.new_url) updateData.new_url = result.new_url;
    
    console.log(`[${getCurrentTime()}] 更新抖音主播信息: ${JSON.stringify(updateData)}`);
    
    // 更新Supabase
    const { error } = await supabase
      .from('anchors')
      .update(updateData)
      .eq('anchor_id', result.anchor_id);
      
    if (error) {
      console.error(`[${getCurrentTime()}] 更新抖音数据失败: ${error.message}`);
      return false;
    } else {
      console.log(`[${getCurrentTime()}] 成功更新抖音数据: ${JSON.stringify(updateData)}`);
      
      
      return true;
    }
  } catch (error) {
    console.error(`[${getCurrentTime()}] 更新抖音数据时发生错误: ${error.message}`);
    return false;
  }
};

  

/**
 * 分页获取主播数据
 * @param {number} pageSize 每页大小
 * @param {number} page 页码（从0开始）
 * @param {string} platform 平台过滤（可选）
 * @returns {Promise<Array>} 主播数据数组
 */
const fetchAnchorsWithPagination = async (pageSize = 100, page = 0, platform = null) => {
  try {
    let query = supabase
      .from('anchors')
      .select('anchor_id, new_url, platform, live_url')
      .eq('nosub', false)
      .order('anchor_id', { ascending: true }); // 使用anchor_id排序以确保分页一致性

    if (platform) {
      query = query.eq('platform', platform);
    }

    // 添加分页
    query = query.range(page * pageSize, (page + 1) * pageSize - 1);

    const { data, error, count } = await query;
    
    if (error) {
      console.error(`[${getCurrentTime()}] 分页获取anchors数据时出错: ${error.message}`);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error(`[${getCurrentTime()}] 分页查询时发生异常: ${error.message}`);
    return [];
  }
};

/**
 * 发送统计信息到Telegram
 * @param {Object} stats 统计信息对象
 */
const sendStatsToTelegram = async (stats) => {
  try {
    // 获取详细统计数据（如果有的话）
    let detailsText = '';
    if (stats.details) {
      detailsText = `
📈 *详细统计*:
  - 检查记录: ${stats.details.totalExamined}条
  - 成功处理: ${stats.details.processed}条
  - 处理出错: ${stats.details.errors}条
  - 跳过记录: ${stats.details.skipped}条`;
      
      // 分类统计错误类型
      const errorCategories = {
        apiFailure: 0,
        processLinkFailure: 0,
        dbUpdateFailure: 0,
        unknownError: 0
      };
      
      // 分类统计跳过原因
      const skipCategories = {
        noLink: 0,
        invalidFormat: 0,
        nonTargetPlatform: 0
      };
      
      // 统计错误分类
      if (stats.details.errorDetails && stats.details.errorDetails.length > 0) {
        stats.details.errorDetails.forEach(error => {
          const errorMsg = error.error.toLowerCase();
          if (errorMsg.includes('api') || errorMsg.includes('请求失败') || errorMsg.includes('timeout')) {
            errorCategories.apiFailure++;
          } else if (errorMsg.includes('处理链接') || errorMsg.includes('返回null') || errorMsg.includes('processDouyinLink')) {
            errorCategories.processLinkFailure++;
          } else if (errorMsg.includes('数据库') || errorMsg.includes('update') || errorMsg.includes('supabase')) {
            errorCategories.dbUpdateFailure++;
          } else {
            errorCategories.unknownError++;
          }
        });
      }
      
      // 统计跳过分类
      if (stats.details.skippedDetails && stats.details.skippedDetails.length > 0) {
        stats.details.skippedDetails.forEach(skipped => {
          const reason = skipped.reason.toLowerCase();
          if (reason.includes('无可用链接')) {
            skipCategories.noLink++;
          } else if (reason.includes('链接格式不符') || reason.includes('格式不符')) {
            skipCategories.invalidFormat++;
          } else if (reason.includes('非目标平台')) {
            skipCategories.nonTargetPlatform++;
          }
        });
      }
      
      // 添加错误分类统计
      detailsText += `\n\n❌ *错误分类统计*:
  - API请求失败: ${errorCategories.apiFailure}条
  - 处理链接失败: ${errorCategories.processLinkFailure}条
  - 数据库更新失败: ${errorCategories.dbUpdateFailure}条
  - 未知错误: ${errorCategories.unknownError}条`;
      
      // 添加跳过分类统计
      detailsText += `\n\n⏭️ *跳过分类统计*:
  - 无可用链接: ${skipCategories.noLink}条
  - 链接格式不符: ${skipCategories.invalidFormat}条
  - 非目标平台: ${skipCategories.nonTargetPlatform}条`;
      
      // 添加错误记录示例
      if (stats.details.errorDetails && stats.details.errorDetails.length > 0) {
        detailsText += `\n\n🔴 *错误记录示例*:`;
        stats.details.errorDetails.forEach((error, index) => {
          if (index < 3) { // 只显示前3条
            detailsText += `\n  ${index + 1}. ${error.anchor_id}: ${error.error}`;
          }
        });
        if (stats.details.errorDetails.length > 3) {
          detailsText += `\n  ... 还有${stats.details.errorDetails.length - 3}条错误`;
        }
      }
    }
    
    const message = `
📊 *抖音监控循环统计*

> 本次循环用时: ${stats.duration.toFixed(2)}秒
🔌 使用API端口: ${getCurrentPort()}
📈 处理任务数量: ${stats.processedCount}条
⚡️ 平均处理速度: 
   - ${stats.perSecond.toFixed(2)}条/秒
   - ${stats.perMinute.toFixed(2)}条/分钟${detailsText}

📆 ${getCurrentTime()}
`;

    await bot.telegram.sendMessage(TELEGRAM_CHAT_ID, message, { parse_mode: 'Markdown' });
    console.log(`[${getCurrentTime()}] 循环统计信息已发送到Telegram`);
  } catch (error) {
    console.error(`[${getCurrentTime()}] 发送Telegram消息失败: ${error.message}`);
  }
};

/**
 * 发送进度信息到Telegram
 * @param {Object} progress 进度信息对象
 */
const sendProgressToTelegram = async (progress) => {
  try {
    // 格式化批次详情信息
    let batchDetails = '';
    if (progress.batchDetails) {
      batchDetails = `
🔄 *批次详情*:
  - 处理: ${progress.batchDetails.processed}条
  - 错误: ${progress.batchDetails.errors}条
  - 跳过: ${progress.batchDetails.skipped}条`;
    }
    
    const message = `
📊 *抖音监控进度报告* #${progress.loopCount}

🔌 *API端口*: ${getCurrentPort()}
🔄 *当前进度*: ${progress.current}/${progress.total} (${(progress.percentage).toFixed(2)}%)
⏱️ *已用时间*: ${progress.elapsedTime.toFixed(2)}秒
🔮 *预计剩余*: ${progress.estimatedRemaining.toFixed(2)}秒
⚡ *当前速度*: ${progress.speed.toFixed(2)}条/秒${batchDetails}

📆 *${getCurrentTime()}*
`;

    await bot.telegram.sendMessage(TELEGRAM_CHAT_ID, message, { parse_mode: 'Markdown' });
    console.log(`[${getCurrentTime()}] 进度信息已发送到Telegram`);
  } catch (error) {
    console.error(`[${getCurrentTime()}] 发送Telegram进度消息失败: ${error.message}`);
  }
};

/**
 * 启动主处理循环，持续处理符合条件的主播记录
 */
const startMainProcessingLoop = async () => {
  console.log(`[${getCurrentTime()}] 启动主处理循环`);
  
  // 循环计数器
  let loopCount = 0;
  
  // 立即开始循环处理
  console.log(`[${getCurrentTime()}] 开始循环处理所有抖音主播`);
  
  // 启动无限循环处理，无间隔
  while (true) {
    try {
      // 记录循环开始时间
      const loopStartTime = new Date();
      
      // 记录本轮处理的主播数量
      let processedCount = 0;
      
      // 当前循环次数
      const currentLoopCount = loopCount + 1;
      
      console.log(`[${getCurrentTime()}] 开始新一轮扫描 #${currentLoopCount}，处理所有符合条件的抖音主播...`);
      
      // 处理抖音平台主播，传递当前循环次数
      const scanResult = await performGlobalScan('douyin', currentLoopCount, false); // 第三个参数false表示不发送中间进度消息
      processedCount = scanResult.totalProcessed;
      
      
      // 计算循环耗时
      const loopEndTime = new Date();
      const loopDuration = (loopEndTime - loopStartTime) / 1000; // 转换为秒
      
      // 更新循环计数
      loopCount++;
      
      // 计算统计数据
      const perSecond = processedCount > 0 ? processedCount / loopDuration : 0;
      const perMinute = perSecond * 60;
      
      // 打印日志
      console.log(`[${getCurrentTime()}] 本轮扫描 #${loopCount} 完成，耗时: ${loopDuration.toFixed(2)} 秒`);
      console.log(`[${getCurrentTime()}] 处理任务数量: ${processedCount}, 平均每秒: ${perSecond.toFixed(2)}, 每分钟: ${perMinute.toFixed(2)}`);
      
      // 发送统计信息到Telegram - 确保每次循环结束后发送
      await sendStatsToTelegram({
        loopCount: loopCount,
        duration: loopDuration,
        processedCount: processedCount,
        perSecond: perSecond,
        perMinute: perMinute,
        details: scanResult // 传递更详细的扫描结果
      });
      
      // 循环结束后轮换API端口
      rotatePort();
      
      console.log(`[${getCurrentTime()}] 立即开始下一轮扫描...`);
      
      // 移除等待时间，直接进入下一轮循环
      // await new Promise(resolve => setTimeout(resolve, 10 * 60 * 1000)); // 10分钟
      
      // 主动释放内存
      global.gc && global.gc();
    } catch (error) {
      console.error(`[${getCurrentTime()}] 主处理循环发生错误: ${error.message}`);
      console.error(`[${getCurrentTime()}] 错误堆栈: ${error.stack}`);
      // 出错后短暂等待再重试，避免错误情况下的死循环
      await new Promise(resolve => setTimeout(resolve, 5 * 1000)); // 5秒
    }
  }
};

/**
 * 执行全局扫描
 * @param {string} platform 平台名称，如'douyin'
 * @param {number} loopCount 循环计数
 * @param {boolean} sendProgressMessages 是否发送中间进度消息
 * @returns {Object} 返回处理统计信息
 */
const performGlobalScan = async (platform = 'douyin', loopCount = 0, sendProgressMessages = false) => {
  console.log(`[${getCurrentTime()}] 开始执行全局扫描，平台: ${platform}`);
    
  // 每次扫描都重置统计变量，避免内存累积
  let allProcessed = 0;
  let allErrors = 0;
  let skippedCount = 0;
  let currentPage = 0;
  const pageSize = 300; // 增加每页数量以减少数据库查询次数
  let hasMoreData = true;
  const concurrencyLimit = 10; // 并发处理的最大主播数量（提高到10以加快处理速度）
  
  // 收集错误和跳过记录的详细信息
  let errorDetails = [];
  let skippedDetails = [];
  
  // 记录扫描开始时间
  const scanStartTime = new Date();
  
  // 获取总记录数以计算进度
  let totalRecords = 0;
  try {
    const { data, count, error } = await supabase
      .from('anchors')
      .select('anchor_id', { count: 'exact' })
      .eq('nosub', false)
      .eq('platform', platform);
      
    if (!error) {
      totalRecords = count;
      console.log(`[${getCurrentTime()}] 总共需要处理 ${totalRecords} 条${platform}记录`);
    }
  } catch (error) {
    console.error(`[${getCurrentTime()}] 获取总记录数时出错: ${error.message}`);
  }
  
  // 进度报告相关变量
  let lastProgressTime = new Date();
  let processedSoFar = 0;
  let totalExamined = 0; // 总共检查过的记录数量
  
  // 使用分页循环处理所有数据
  while (hasMoreData) {
    // 获取指定平台的数据
    const anchors = await fetchAnchorsWithPagination(pageSize, currentPage, platform);
    console.log(`[${getCurrentTime()}] 获取第 ${currentPage + 1} 页数据，共 ${anchors.length} 条${platform}记录`);
    
    if (anchors.length === 0) {
      hasMoreData = false;
      continue;
    }
    
    let pageProcessed = 0;
    let pageErrors = 0;
    let pageSkipped = 0;
    
    // 分批处理，每批并发执行concurrencyLimit个任务
    for (let i = 0; i < anchors.length; i += concurrencyLimit) {
      const batch = anchors.slice(i, i + concurrencyLimit);
      const batchStartTime = new Date();
      
      console.log(`[${getCurrentTime()}] 处理批次 ${Math.floor(i/concurrencyLimit) + 1}/${Math.ceil(anchors.length/concurrencyLimit)}, 并发处理 ${batch.length} 条记录...`);
      
      const results = await Promise.all(batch.map(async (anchor) => {
        // 只处理可以提取web_rid的
        if (anchor.new_url) {
          try {
            // 尝试提取webcast_id预检查
            const douyinUrl = anchor.new_url;
            if (douyinUrl && (douyinUrl.includes('live.douyin.com/') || douyinUrl.includes('v.douyin.com/'))) {
              console.log(`[${getCurrentTime()}] 并发处理中: ${anchor.anchor_id} - ${douyinUrl}`);
              
              try {
                const result = await processDouyinLink(anchor.anchor_id, douyinUrl);
                if (result) {
                  const updated = await updateDouyinAnchorInfo(result);
                  if (updated) {
                    return { status: 'processed', anchor_id: anchor.anchor_id };
                  } else {
                    // 更新数据库失败
                    return { status: 'error', anchor_id: anchor.anchor_id, error: '数据库更新失败' };
                  }
                } else {
                  // processDouyinLink 返回 null
                  return { status: 'error', anchor_id: anchor.anchor_id, error: '处理链接返回null' };
                }
              } catch (error) {
                console.error(`[${getCurrentTime()}] 处理抖音链接出错 (anchor_id: ${anchor.anchor_id}): ${error.message}`);
                if (error.response) {
                  console.error(`[${getCurrentTime()}] API响应状态: ${error.response.status}`);
                  console.error(`[${getCurrentTime()}] API响应数据: ${JSON.stringify(error.response.data || {})}`);
                }
                return { status: 'error', anchor_id: anchor.anchor_id, error: error.message };
              }
            } else {
              console.log(`[${getCurrentTime()}] 链接格式不符合要求，跳过: ${anchor.anchor_id} - ${douyinUrl}`);
              return { status: 'skipped', anchor_id: anchor.anchor_id, reason: '链接格式不符' };
            }
          } catch (error) {
            console.error(`[${getCurrentTime()}] 全局扫描中处理抖音链接出错: ${error.message}`);
            return { status: 'error', anchor_id: anchor.anchor_id, error: error.message };
          }
        } else {
          console.log(`[${getCurrentTime()}] 无可用链接，跳过: ${anchor.anchor_id}`);
          return { status: 'skipped', anchor_id: anchor.anchor_id, reason: '无可用链接' };
        }
        
        // 这行代码不应该执行到，但为了安全起见
        console.log(`[${getCurrentTime()}] 无法处理主播: ${anchor.anchor_id}`);
        return { status: 'error', anchor_id: anchor.anchor_id, error: '未知处理错误' }; 
      }));
      
      // 统计这一批的处理结果
      for (const result of results) {
        if (result.status === 'processed') pageProcessed++;
        else if (result.status === 'error') {
          pageErrors++;
          errorDetails.push({
            anchor_id: result.anchor_id,
            error: result.error || '未知错误'
          });
        }
        else if (result.status === 'skipped') {
          pageSkipped++;
          skippedDetails.push({
            anchor_id: result.anchor_id,
            reason: result.reason || '未知原因'
          });
        }
      }
      
      // 更新总处理数
      const batchSize = batch.length;
      processedSoFar += batchSize;
      totalExamined += batchSize;
      
      // 计算批次处理时间
      const batchEndTime = new Date();
      const batchDuration = (batchEndTime - batchStartTime) / 1000;
      
      // 更新当前进度
      const currentTime = new Date();
      const elapsedSeconds = (currentTime - scanStartTime) / 1000;
      
      // 进度百分比
      const processedPercentage = totalRecords > 0 ? (processedSoFar / totalRecords) * 100 : 0;
      
      // 计算处理速度和预估剩余时间
      const speed = processedSoFar > 0 ? processedSoFar / elapsedSeconds : 0;
      const estimatedRemaining = speed > 0 && totalRecords > 0 ? 
                             (totalRecords - processedSoFar) / speed : 0;
      
      // 批次详细信息
      console.log(`[${getCurrentTime()}] 批次处理完成，耗时: ${batchDuration.toFixed(2)}秒, 详情: 处理=${pageProcessed}, 错误=${pageErrors}, 跳过=${pageSkipped}`);
      
      // 输出批处理进度日志 - 每个批次都会显示
      if (totalRecords > 0) {
        console.log(`[${getCurrentTime()}] 处理进度: ${processedSoFar}/${totalRecords} (${processedPercentage.toFixed(2)}%), 速度: ${speed.toFixed(2)}条/秒, 预计剩余: ${estimatedRemaining.toFixed(2)}秒`);
      } else {
        console.log(`[${getCurrentTime()}] 处理进度: ${processedSoFar} 已处理, 速度: ${speed.toFixed(2)}条/秒`);
      }
      
      // 批次内处理速度
      const batchSpeed = batchSize / batchDuration;
      console.log(`[${getCurrentTime()}] 批次处理速度: ${batchSpeed.toFixed(2)}条/秒`);
      
      // 根据参数决定是否发送中间进度消息
      if (sendProgressMessages) {
        const timeSinceLastProgress = (currentTime - lastProgressTime) / 1000;
        // 每30秒或在关键进度点发送一次进度报告
        if ((timeSinceLastProgress >= 30 && totalRecords > 100) || 
            (processedPercentage >= 25 && processedPercentage < 30) || 
            (processedPercentage >= 50 && processedPercentage < 55) || 
            (processedPercentage >= 75 && processedPercentage < 80)) {
          
          lastProgressTime = currentTime;
          
          // 发送进度报告
          await sendProgressToTelegram({
            loopCount: loopCount,
            current: processedSoFar,
            total: totalRecords,
            percentage: processedPercentage,
            elapsedTime: elapsedSeconds,
            estimatedRemaining: estimatedRemaining,
            speed: speed,
            batchDetails: {
              processed: pageProcessed,
              errors: pageErrors,
              skipped: pageSkipped
            }
          });
        }
      }
    }
    
    // 更新统计数据
    allProcessed += pageProcessed;
    allErrors += pageErrors;
    skippedCount += pageSkipped;
    
    console.log(`[${getCurrentTime()}] 第 ${currentPage + 1} 页处理完成: ${pageProcessed} 条直接处理, ${pageErrors} 条处理错误, ${pageSkipped} 条跳过`);
    
    // 判断是否还有更多数据
    if (anchors.length < pageSize) {
      hasMoreData = false;
    }
    
    currentPage++;
    
    // 减少延迟时间，提高处理速度
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  // 计算扫描耗时
  const scanEndTime = new Date();
  const scanDuration = (scanEndTime - scanStartTime) / 1000; // 转换为秒
  
  console.log(`[${getCurrentTime()}] 全局扫描完成: 共直接处理 ${allProcessed} 条${platform}链接，处理错误 ${allErrors} 条，跳过 ${skippedCount} 条`);
  console.log(`[${getCurrentTime()}] 总共检查 ${totalExamined} 条记录，实际处理 ${allProcessed} 条，跳过或出错 ${skippedCount + allErrors} 条`);
  console.log(`[${getCurrentTime()}] 本轮扫描总耗时: ${scanDuration} 秒`);

  
  // 返回处理统计信息
  return {
    totalExamined: totalExamined,
    totalProcessed: allProcessed,
    processed: allProcessed,
    errors: allErrors,
    skipped: skippedCount,
    duration: scanDuration,
    errorDetails: errorDetails,
    skippedDetails: skippedDetails
  };
};

/**
 * 发送所有符合条件的主播记录进行处理
 * 筛选条件: nosub=false 且 platform='douyin'
 */
const sendAllAnchorsToProcess = async () => {
  console.log(`[${getCurrentTime()}] 获取所有 nosub 为 false 且 platform 为 douyin 的抖音主播...`);
   
  const { data, error } = await supabase  
    .from('anchors')
    .select('anchor_id, stream_url, new_url, new_url, live_url')
    .eq('nosub', false)
    .eq('platform', 'douyin');
    
  if (error) {
    console.error(`[${getCurrentTime()}] 获取 anchors 数据时出错: ${error}`);
    return;
  }
    
  console.log(`[${getCurrentTime()}] 找到 ${data.length} 条符合条件的抖音主播记录，开始处理...`);
  
  let processedCount = 0;
  let totalCount = data.length;
  const concurrencyLimit = 10; // 并发处理的最大主播数量
  
  // 记录处理开始时间
  const processStartTime = new Date();
  
  // 分批处理，每批并发执行concurrencyLimit个任务
  for (let i = 0; i < data.length; i += concurrencyLimit) {
    const batch = data.slice(i, i + concurrencyLimit);
    
    const results = await Promise.all(batch.map(async (anchor) => {
      try {
        const douyinUrl = anchor.new_url || anchor.live_url;
        
        if (douyinUrl && (douyinUrl.includes('live.douyin.com/') || douyinUrl.includes('v.douyin.com/'))) {
          console.log(`[${getCurrentTime()}] 并发处理主播 ${anchor.anchor_id} 的链接: ${douyinUrl}`);
          
          // 处理抖音链接
          const result = await processDouyinLink(anchor.anchor_id, douyinUrl);
          
          if (result) {
            await updateDouyinAnchorInfo(result);
            return { status: 'processed', anchor_id: anchor.anchor_id };
          }
        }
        
        // 处理失败或无法处理的情况
        console.log(`[${getCurrentTime()}] 无法处理主播 ${anchor.anchor_id}`);
        return { status: 'skipped', anchor_id: anchor.anchor_id, reason: '处理失败' };
        
      } catch (error) {
        console.error(`[${getCurrentTime()}] 处理主播 ${anchor.anchor_id} 时出错: ${error.message}`);
        return { status: 'error', anchor_id: anchor.anchor_id };
      }
    }));
    
    // 统计这一批的处理结果
    for (const result of results) {
      if (result.status === 'processed') processedCount++;
    }
    
    console.log(`[${getCurrentTime()}] 批处理进度: ${Math.min(i + concurrencyLimit, totalCount)}/${totalCount}`);
  }
  
  // 计算处理耗时
  const processEndTime = new Date();
  const processDuration = (processEndTime - processStartTime) / 1000; // 转换为秒
  
  console.log(`[${getCurrentTime()}] 初始化处理完成: 成功处理 ${processedCount}/${totalCount} 条抖音主播记录，耗时: ${processDuration} 秒`);  
};





// 测试Telegram连接
const testTelegramConnection = async () => {
  try {
    const testMessage = `
🚀 *抖音监控程序启动*

📅 启动时间: ${getCurrentTime()}
🤖 Bot Token: ${TELEGRAM_BOT_TOKEN.substring(0, 10)}...
💬 Chat ID: ${TELEGRAM_CHAT_ID}
✅ Telegram连接测试成功
`;
    
    await bot.telegram.sendMessage(TELEGRAM_CHAT_ID, testMessage, { parse_mode: 'Markdown' });
    console.log(`[${getCurrentTime()}] Telegram连接测试成功`);
    return true;
  } catch (error) {
    console.error(`[${getCurrentTime()}] Telegram连接测试失败: ${error.message}`);
    if (error.response) {
      console.error(`[${getCurrentTime()}] 错误详情: ${JSON.stringify(error.response.data || {})}`);
    }
    return false;
  }
};

// 全局变量，用于控制程序退出
let shouldExit = false;
let statusCheckInterval = null;

/**
 * 监听 does 表的 status 字段变化
 */
const monitorDoesStatus = async () => {
  console.log(`[${getCurrentTime()}] 开始监听 does 表的 status 字段变化`);
  
  let lastStatus = null;
  
  // 首次获取当前状态
  try {
    const { data, error } = await supabase
      .from('does')
      .select('status')
      .limit(1)
      .single();
    
    if (!error && data) {
      lastStatus = data.status;
      console.log(`[${getCurrentTime()}] does 表当前 status 值: ${lastStatus}`);
    }
  } catch (error) {
    console.error(`[${getCurrentTime()}] 获取 does 表初始状态失败: ${error.message}`);
  }
  
  // 定期检查状态变化
  statusCheckInterval = setInterval(async () => {
    try {
      const { data, error } = await supabase
        .from('does')
        .select('status')
        .limit(1)
        .single();
      
      if (!error && data) {
        if (lastStatus !== null && data.status !== lastStatus) {
          console.log(`[${getCurrentTime()}] 检测到 does 表 status 字段变化: ${lastStatus} -> ${data.status}`);
          console.log(`[${getCurrentTime()}] 准备退出程序...`);
          
          // 发送退出通知到 Telegram
          try {
            await bot.telegram.sendMessage(TELEGRAM_CHAT_ID, 
              `🛑 *程序退出通知*\n\n检测到 does 表 status 字段变化\n旧值: ${lastStatus}\n新值: ${data.status}\n\n程序即将退出...\n\n📅 ${getCurrentTime()}`, 
              { parse_mode: 'Markdown' }
            );
          } catch (tgError) {
            console.error(`[${getCurrentTime()}] 发送退出通知失败: ${tgError.message}`);
          }
          
          // 设置退出标志
          shouldExit = true;
          
          // 清理定时器
          if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
          }
          
          // 延迟1秒后退出，确保消息发送完成
          setTimeout(() => {
            console.log(`[${getCurrentTime()}] 程序正常退出`);
            process.exit(0);
          }, 1000);
        }
        lastStatus = data.status;
      }
    } catch (error) {
      console.error(`[${getCurrentTime()}] 检查 does 表状态时出错: ${error.message}`);
    }
  }, 5000); // 每5秒检查一次
};

// 修改主处理循环，添加退出检查
const startMainProcessingLoopWithExitCheck = async () => {
  const originalLoop = startMainProcessingLoop;
  
  // 包装原始循环函数
  const wrappedLoop = async () => {
    console.log(`[${getCurrentTime()}] 启动带退出检查的主处理循环`);
    
    // 启动状态监听
    await monitorDoesStatus();
    
    // 循环计数器
    let loopCount = 0;
    
    // 立即开始循环处理
    console.log(`[${getCurrentTime()}] 开始循环处理所有抖音主播`);
    
    // 启动无限循环处理，无间隔
    while (!shouldExit) {
      try {
        // 记录循环开始时间
        const loopStartTime = new Date();
        
        // 记录本轮处理的主播数量
        let processedCount = 0;
        
        // 当前循环次数
        const currentLoopCount = loopCount + 1;
        
        console.log(`[${getCurrentTime()}] 开始新一轮扫描 #${currentLoopCount}，处理所有符合条件的抖音主播...`);
        
        // 处理抖音平台主播，传递当前循环次数
        const scanResult = await performGlobalScan('douyin', currentLoopCount, false); // 第三个参数false表示不发送中间进度消息
        processedCount = scanResult.totalProcessed;
        
        // 计算循环耗时
        const loopEndTime = new Date();
        const loopDuration = (loopEndTime - loopStartTime) / 1000; // 转换为秒
        
        // 更新循环计数
        loopCount++;
        
        // 计算统计数据
        const perSecond = processedCount > 0 ? processedCount / loopDuration : 0;
        const perMinute = perSecond * 60;
        
        // 打印日志
        console.log(`[${getCurrentTime()}] 本轮扫描 #${loopCount} 完成，耗时: ${loopDuration.toFixed(2)} 秒`);
        console.log(`[${getCurrentTime()}] 处理任务数量: ${processedCount}, 平均每秒: ${perSecond.toFixed(2)}, 每分钟: ${perMinute.toFixed(2)}`);
        
        // 发送统计信息到Telegram - 确保每次循环结束后发送
        await sendStatsToTelegram({
          loopCount: loopCount,
          duration: loopDuration,
          processedCount: processedCount,
          perSecond: perSecond,
          perMinute: perMinute,
          details: scanResult // 传递更详细的扫描结果
        });
        
        // 循环结束后轮换API端口
        rotatePort();
        
        // 检查是否应该退出
        if (shouldExit) {
          console.log(`[${getCurrentTime()}] 收到退出信号，停止处理循环`);
          break;
        }
        
        console.log(`[${getCurrentTime()}] 立即开始下一轮扫描...`);
        
        // 主动释放内存
        global.gc && global.gc();
      } catch (error) {
        console.error(`[${getCurrentTime()}] 主处理循环发生错误: ${error.message}`);
        console.error(`[${getCurrentTime()}] 错误堆栈: ${error.stack}`);
        // 出错后短暂等待再重试，避免错误情况下的死循环
        await new Promise(resolve => setTimeout(resolve, 5 * 1000)); // 5秒
      }
    }
    
    console.log(`[${getCurrentTime()}] 主处理循环已停止`);
  };
  
  return wrappedLoop();
};

// 启动主处理循环
console.log(`[${getCurrentTime()}] 程序启动，开始主处理循环`);

// 先测试Telegram连接，然后启动主循环
testTelegramConnection().then(success => {
  if (success) {
    console.log(`[${getCurrentTime()}] Telegram连接正常，启动主处理循环`);
    startMainProcessingLoopWithExitCheck().catch(error => {
      console.error(`[${getCurrentTime()}] 启动主处理循环失败: ${error.message}`);
      process.exit(1);
    });
  } else {
    console.error(`[${getCurrentTime()}] Telegram连接失败，程序退出`);
    process.exit(1);
  }
});

// 优雅退出处理
process.on('SIGINT', async () => {
  console.log(`[${getCurrentTime()}] 收到 SIGINT 信号，准备退出...`);
  shouldExit = true;
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval);
  }
  setTimeout(() => {
    process.exit(0);
  }, 1000);
});

process.on('SIGTERM', async () => {
  console.log(`[${getCurrentTime()}] 收到 SIGTERM 信号，准备退出...`);
  shouldExit = true;
  if (statusCheckInterval) {
    clearInterval(statusCheckInterval);
  }
  setTimeout(() => {
    process.exit(0);
  }, 1000);
});

